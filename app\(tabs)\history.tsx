import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { ScanHistoryItem, storageService } from '@/services/storageService';
import { useFocusEffect } from '@react-navigation/native';
import React, { useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    FlatList,
    Platform,
    SafeAreaView,
    StyleSheet,
    TouchableOpacity,
    View,
} from 'react-native';

export default function HistoryScreen() {
  const [scanHistory, setScanHistory] = useState<ScanHistoryItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useFocusEffect(
    React.useCallback(() => {
      loadScanHistory();
    }, [])
  );

  const loadScanHistory = async () => {
    try {
      setIsLoading(true);
      const history = await storageService.getScanHistory();
      setScanHistory(history);
    } catch (error) {
      console.error('Error loading scan history:', error);
      Alert.alert('Error', 'Failed to load scan history');
    } finally {
      setIsLoading(false);
    }
  };

  const clearHistory = () => {
    Alert.alert(
      'Clear History',
      'Are you sure you want to clear all scan history?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: async () => {
            try {
              await storageService.clearScanHistory();
              setScanHistory([]);
            } catch (error) {
              console.error('Error clearing history:', error);
              Alert.alert('Error', 'Failed to clear history');
            }
          },
        },
      ]
    );
  };

  const showItemDetails = (item: ScanHistoryItem) => {
    console.log('showItemDetails called for:', item.result.name);

    const result = item.result;
    const confidencePercent = Math.round(result.confidence * 100);

    let message = `${result.description || 'Object identified!'}\n\nConfidence: ${confidencePercent}%`;

    // Add category information
    if (result.category && result.category !== 'unknown') {
      message += `\nCategory: ${result.category.charAt(0).toUpperCase() + result.category.slice(1)}`;
    }

    // Add additional info if available
    if (result.additionalInfo) {
      if (result.additionalInfo.brand) {
        message += `\nBrand: ${result.additionalInfo.brand}`;
      }
      if (result.additionalInfo.type) {
        message += `\nType: ${result.additionalInfo.type}`;
      }
      if (result.additionalInfo.ingredients) {
        message += `\nIngredients: ${result.additionalInfo.ingredients}`;
      }
    }

    // Add scan date
    message += `\n\nScanned: ${item.timestamp.toLocaleDateString()} at ${item.timestamp.toLocaleTimeString()}`;

    // For web, use window.alert as fallback since React Native Alert might not work
    if (Platform.OS === 'web') {
      const shouldShowShopping = result.purchaseLinks && result.purchaseLinks.length > 0;
      if (shouldShowShopping) {
        message += `\n\n🛒 ${result.purchaseLinks.length} shopping options available!`;
      }

      window.alert(`${result.name}\n\n${message}`);

      if (shouldShowShopping) {
        const showShopping = window.confirm('Would you like to see shopping options?');
        if (showShopping) {
          showShoppingOptions(result);
        }
      }
    } else {
      const buttons = [
        { text: 'OK', style: 'default' as const }
      ];

      // Add shopping button if purchase links are available
      if (result.purchaseLinks && result.purchaseLinks.length > 0) {
        message += `\n\n🛒 ${result.purchaseLinks.length} shopping options available!`;
        buttons.push({
          text: 'Shop Now',
          onPress: () => {
            showShoppingOptions(result);
          }
        });
      }

      Alert.alert(result.name, message, buttons);
    }
  };

  const showShoppingOptions = (result: any) => {
    if (!result.purchaseLinks || result.purchaseLinks.length === 0) return;

    if (Platform.OS === 'web') {
      // For web, show a simple list of options
      const linksList = result.purchaseLinks.map((link: any) =>
        `${link.platform}: ${link.url}`
      ).join('\n');

      window.alert(`Shopping Options:\n\n${linksList}\n\nCopy and paste the URL you want to visit.`);
    } else {
      const options = result.purchaseLinks.map((link: any) => ({
        text: link.platform,
        onPress: () => {
          console.log(`Opening ${link.platform}: ${link.url}`);
          Alert.alert('Shopping Link', `Would open: ${link.platform}\n${link.url}`);
        }
      }));

      options.push({ text: 'Cancel', style: 'cancel' as const });

      Alert.alert(
        'Shop for this item',
        'Choose where you\'d like to shop:',
        options
      );
    }
  };

  const renderHistoryItem = ({ item }: { item: ScanHistoryItem }) => (
    <TouchableOpacity style={styles.historyItem} onPress={() => showItemDetails(item)}>
      <View style={styles.itemContent}>
        <View style={styles.itemInfo}>
          <ThemedText style={styles.itemName}>{item.result.name}</ThemedText>
          <ThemedText style={styles.itemCategory}>{item.result.category}</ThemedText>
          <ThemedText style={styles.itemTimestamp}>
            {item.timestamp.toLocaleDateString()} {item.timestamp.toLocaleTimeString()}
          </ThemedText>
        </View>
        <View style={styles.itemActions}>
          <ThemedText style={styles.confidence}>
            {Math.round(item.result.confidence * 100)}%
          </ThemedText>
          <IconSymbol name="chevron.right" size={20} color="#666" />
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <IconSymbol name="clock" size={80} color="#ccc" />
      <ThemedText style={styles.emptyTitle}>No Scans Yet</ThemedText>
      <ThemedText style={styles.emptySubtitle}>
        Start scanning objects to see your history here
      </ThemedText>
    </View>
  );

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <ThemedView style={styles.loadingContainer}>
          <ActivityIndicator size="large" />
          <ThemedText>Loading history...</ThemedText>
        </ThemedView>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ThemedView style={styles.header}>
        <ThemedText style={styles.title}>Scan History</ThemedText>
        {scanHistory.length > 0 && (
          <TouchableOpacity onPress={clearHistory} style={styles.clearButton}>
            <ThemedText style={styles.clearButtonText}>Clear</ThemedText>
          </TouchableOpacity>
        )}
      </ThemedView>

      <FlatList
        data={scanHistory}
        renderItem={renderHistoryItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  clearButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    backgroundColor: '#ff4444',
  },
  clearButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  listContainer: {
    flexGrow: 1,
    paddingHorizontal: 20,
  },
  historyItem: {
    backgroundColor: 'white',
    borderRadius: 12,
    marginVertical: 6,
    boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
    elevation: 3,
  },
  itemContent: {
    flexDirection: 'row',
    padding: 16,
    alignItems: 'center',
  },
  itemInfo: {
    flex: 1,
  },
  itemName: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
  },
  itemCategory: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  itemTimestamp: {
    fontSize: 12,
    color: '#999',
  },
  itemActions: {
    alignItems: 'center',
    gap: 8,
  },
  confidence: {
    fontSize: 14,
    fontWeight: '600',
    color: '#007AFF',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 20,
    marginBottom: 8,
    color: '#666',
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#999',
    textAlign: 'center',
    paddingHorizontal: 40,
  },
});
