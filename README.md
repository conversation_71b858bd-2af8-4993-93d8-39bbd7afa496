# What's That? - Universal Object Scanner 📱

A React Native mobile app built with Expo that uses AI to identify any object through your camera. Point your phone at anything - products, plants, animals, landmarks - and get instant identification with purchase links, care instructions, and detailed information.

## Features ✨

- **Universal Object Recognition**: Identify products, plants, animals, landmarks, and more
- **AI-Powered**: Uses OpenAI Vision API and Google Vision API for accurate identification
- **Purchase Links**: Automatic affiliate links for products (Amazon, eBay, Google Shopping)
- **Scan History**: Save and organize your discoveries
- **Camera & Gallery**: Take photos or upload from gallery
- **Cross-Platform**: Works on iOS, Android, and Web

## Tech Stack 🛠️

- **Frontend**: React Native with Expo
- **AI Services**: OpenAI Vision API, Google Vision API
- **Storage**: AsyncStorage for local data
- **Navigation**: Expo Router
- **Camera**: Expo Camera
- **Styling**: React Native StyleSheet

## Setup Instructions 🚀

### 1. Install Dependencies

```bash
npm install
```

### 2. Configure API Keys

Copy the environment file and add your API keys:

```bash
cp .env.example .env
```

Edit `.env` and add your API keys:

```env
EXPO_PUBLIC_OPENAI_API_KEY=your_openai_api_key_here
EXPO_PUBLIC_GOOGLE_VISION_API_KEY=your_google_vision_api_key_here
```

#### Getting API Keys:

- **OpenAI API Key**: Get from [OpenAI Platform](https://platform.openai.com/api-keys)
- **Google Vision API Key**: Get from [Google Cloud Console](https://console.cloud.google.com/)

### 3. Start the Development Server

```bash
npm start
```

### 4. Run on Device/Simulator

- **iOS**: Press `i` or scan QR code with Camera app
- **Android**: Press `a` or scan QR code with Expo Go
- **Web**: Press `w` or open http://localhost:8081

## Project Structure 📁

```
whatsthat/
├── app/                    # App screens (Expo Router)
│   ├── (tabs)/            # Tab navigation
│   │   ├── index.tsx      # Scanner screen
│   │   ├── history.tsx    # Scan history
│   │   └── explore.tsx    # Explore/settings
│   └── _layout.tsx        # Root layout
├── services/              # Business logic
│   ├── aiService.ts       # AI integration
│   └── storageService.ts  # Local storage
├── components/            # Reusable components
└── constants/             # App constants
```

## Usage 📖

1. **Scan Objects**: Open the app and point camera at any object
2. **Take Photo**: Tap the capture button to identify the object
3. **Upload Image**: Tap gallery icon to select from photos
4. **View Results**: See identification results with confidence score
5. **Purchase Links**: For products, get direct purchase links
6. **Save History**: All scans are automatically saved to history

## Monetization 💰

The app includes built-in monetization through:

- **Affiliate Links**: Amazon, eBay, Google Shopping
- **Premium Features**: Enhanced scanning, batch processing
- **Sponsored Results**: Featured products in scan results

## Development Roadmap 🗺️

### Phase 1 - MVP ✅
- [x] Camera scanning interface
- [x] AI object identification
- [x] Basic results display
- [x] Scan history storage

### Phase 2 - Features (In Progress)
- [ ] Detailed results screen
- [ ] Purchase link integration
- [ ] Social sharing
- [ ] Search functionality

### Phase 3 - Polish
- [ ] Premium subscription
- [ ] Offline mode
- [ ] Performance optimization
- [ ] App store deployment

## Contributing 🤝

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License 📄

This project is licensed under the MIT License.

## Support 💬

For support, create an issue on GitHub.
