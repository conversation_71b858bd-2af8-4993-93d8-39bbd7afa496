import AsyncStorage from '@react-native-async-storage/async-storage';
import { IdentificationResult } from './aiService';

export interface ScanHistoryItem {
  id: string;
  timestamp: Date;
  imageUri: string;
  result: IdentificationResult;
}

class StorageService {
  private readonly SCAN_HISTORY_KEY = 'scan_history';
  private readonly MAX_HISTORY_ITEMS = 100;

  /**
   * Save a scan result to history
   */
  async saveScanResult(imageUri: string, result: IdentificationResult): Promise<void> {
    try {
      const historyItem: ScanHistoryItem = {
        id: Date.now().toString(),
        timestamp: new Date(),
        imageUri,
        result,
      };

      const existingHistory = await this.getScanHistory();
      const updatedHistory = [historyItem, ...existingHistory];

      // Keep only the most recent items
      const trimmedHistory = updatedHistory.slice(0, this.MAX_HISTORY_ITEMS);

      await AsyncStorage.setItem(
        this.SCAN_HISTORY_KEY,
        JSON.stringify(trimmedHistory)
      );
    } catch (error) {
      console.error('Error saving scan result:', error);
      throw new Error('Failed to save scan result');
    }
  }

  /**
   * Get all scan history
   */
  async getScanHistory(): Promise<ScanHistoryItem[]> {
    try {
      const historyJson = await AsyncStorage.getItem(this.SCAN_HISTORY_KEY);
      
      if (!historyJson) {
        return [];
      }

      const history = JSON.parse(historyJson);
      
      // Convert timestamp strings back to Date objects
      return history.map((item: any) => ({
        ...item,
        timestamp: new Date(item.timestamp),
      }));
    } catch (error) {
      console.error('Error loading scan history:', error);
      return [];
    }
  }

  /**
   * Clear all scan history
   */
  async clearScanHistory(): Promise<void> {
    try {
      await AsyncStorage.removeItem(this.SCAN_HISTORY_KEY);
    } catch (error) {
      console.error('Error clearing scan history:', error);
      throw new Error('Failed to clear scan history');
    }
  }

  /**
   * Delete a specific scan from history
   */
  async deleteScanResult(id: string): Promise<void> {
    try {
      const history = await this.getScanHistory();
      const updatedHistory = history.filter(item => item.id !== id);
      
      await AsyncStorage.setItem(
        this.SCAN_HISTORY_KEY,
        JSON.stringify(updatedHistory)
      );
    } catch (error) {
      console.error('Error deleting scan result:', error);
      throw new Error('Failed to delete scan result');
    }
  }

  /**
   * Get scan statistics
   */
  async getScanStats(): Promise<{
    totalScans: number;
    categoryCounts: Record<string, number>;
    averageConfidence: number;
  }> {
    try {
      const history = await this.getScanHistory();
      
      const categoryCounts: Record<string, number> = {};
      let totalConfidence = 0;

      history.forEach(item => {
        const category = item.result.category;
        categoryCounts[category] = (categoryCounts[category] || 0) + 1;
        totalConfidence += item.result.confidence;
      });

      return {
        totalScans: history.length,
        categoryCounts,
        averageConfidence: history.length > 0 ? totalConfidence / history.length : 0,
      };
    } catch (error) {
      console.error('Error getting scan stats:', error);
      return {
        totalScans: 0,
        categoryCounts: {},
        averageConfidence: 0,
      };
    }
  }

  /**
   * Search scan history
   */
  async searchScanHistory(query: string): Promise<ScanHistoryItem[]> {
    try {
      const history = await this.getScanHistory();
      const lowerQuery = query.toLowerCase();
      
      return history.filter(item => 
        item.result.name.toLowerCase().includes(lowerQuery) ||
        item.result.category.toLowerCase().includes(lowerQuery) ||
        (item.result.description && item.result.description.toLowerCase().includes(lowerQuery))
      );
    } catch (error) {
      console.error('Error searching scan history:', error);
      return [];
    }
  }

  /**
   * Export scan history as JSON
   */
  async exportScanHistory(): Promise<string> {
    try {
      const history = await this.getScanHistory();
      return JSON.stringify(history, null, 2);
    } catch (error) {
      console.error('Error exporting scan history:', error);
      throw new Error('Failed to export scan history');
    }
  }

  /**
   * Import scan history from JSON
   */
  async importScanHistory(jsonData: string): Promise<void> {
    try {
      const importedHistory = JSON.parse(jsonData);
      
      // Validate the data structure
      if (!Array.isArray(importedHistory)) {
        throw new Error('Invalid history data format');
      }

      // Convert timestamp strings to Date objects and validate structure
      const validatedHistory = importedHistory.map((item: any) => {
        if (!item.id || !item.timestamp || !item.result) {
          throw new Error('Invalid history item structure');
        }
        
        return {
          ...item,
          timestamp: new Date(item.timestamp),
        };
      });

      await AsyncStorage.setItem(
        this.SCAN_HISTORY_KEY,
        JSON.stringify(validatedHistory)
      );
    } catch (error) {
      console.error('Error importing scan history:', error);
      throw new Error('Failed to import scan history');
    }
  }
}

export const storageService = new StorageService();
