import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { aiService, IdentificationResult } from '@/services/aiService';
import { storageService } from '@/services/storageService';
import * as ImagePicker from 'expo-image-picker';
import React, { useEffect, useRef, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    Dimensions,
    Platform,
    SafeAreaView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View
} from 'react-native';

// Platform-specific imports
let CameraView: any = null;
let CameraType: any = null;
let useCameraPermissions: any = null;

if (Platform.OS !== 'web') {
  const cameraModule = require('expo-camera');
  CameraView = cameraModule.CameraView;
  CameraType = cameraModule.CameraType;
  useCameraPermissions = cameraModule.useCameraPermissions;
}

const { width, height } = Dimensions.get('window');

export default function ScannerScreen() {
  const [facing, setFacing] = useState<any>('back');
  const [permission, requestPermission] = Platform.OS !== 'web' ? useCameraPermissions() : [{ granted: true }, () => {}];
  const [isScanning, setIsScanning] = useState(false);
  const cameraRef = useRef<any>(null);

  // Web-specific state
  const [isWebMode, setIsWebMode] = useState(Platform.OS === 'web');

  // Check AI service configuration on mount
  useEffect(() => {
    if (!aiService.isConfigured()) {
      console.warn('AI services are not properly configured. Object identification may not work.');
      if (__DEV__) {
        Alert.alert(
          'Configuration Warning',
          'AI services are not properly configured. Please check your environment variables for EXPO_PUBLIC_OPENAI_API_KEY and EXPO_PUBLIC_GOOGLE_VISION_API_KEY.',
          [{ text: 'OK' }]
        );
      }
    }
  }, []);

  if (!permission) {
    // Camera permissions are still loading
    return (
      <SafeAreaView style={styles.container}>
        <ThemedView style={styles.loadingContainer}>
          <ActivityIndicator size="large" />
          <ThemedText>Loading camera...</ThemedText>
        </ThemedView>
      </SafeAreaView>
    );
  }

  if (!permission.granted) {
    // Camera permissions are not granted yet
    return (
      <SafeAreaView style={styles.container}>
        <ThemedView style={styles.permissionContainer}>
          <ThemedText style={styles.permissionText}>
            We need your permission to show the camera
          </ThemedText>
          <TouchableOpacity style={styles.permissionButton} onPress={requestPermission}>
            <Text style={styles.permissionButtonText}>Grant Permission</Text>
          </TouchableOpacity>
        </ThemedView>
      </SafeAreaView>
    );
  }

  function toggleCameraFacing() {
    setFacing(current => (current === 'back' ? 'front' : 'back'));
  }

  async function takePicture() {
    if (cameraRef.current && !isScanning) {
      setIsScanning(true);
      try {
        const photo = await cameraRef.current.takePictureAsync({
          quality: 0.7, // Reduced for better performance
          base64: true,
          skipProcessing: false, // Enable processing for better compression
        });

        if (photo && photo.base64) {
          await processImage(photo.uri, photo.base64);
        }
      } catch (error) {
        console.error('Error taking picture:', error);
        Alert.alert('Error', 'Failed to take picture');
      } finally {
        setIsScanning(false);
      }
    }
  }

  async function pickImage() {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: [ImagePicker.MediaType.Images],
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
      base64: true,
    });

    if (!result.canceled && result.assets[0] && result.assets[0].base64) {
      setIsScanning(true);
      try {
        await processImage(result.assets[0].uri, result.assets[0].base64);
      } catch (error) {
        console.error('Error processing image:', error);
        Alert.alert('Error', 'Failed to process image');
      } finally {
        setIsScanning(false);
      }
    }
  }

  async function processImage(imageUri: string, base64: string) {
    try {
      console.log('Processing image...');

      // Identify the object using AI
      const result = await aiService.identifyObject(base64);
      console.log('Identification result:', result);

      // Save to history
      await storageService.saveScanResult(imageUri, result);
      console.log('Result saved to history');

      // Show results to user
      showResults(result);

    } catch (error) {
      console.error('Error processing image:', error);

      // Show user-friendly error message
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

      Alert.alert(
        'Identification Failed',
        `Unable to identify the object: ${errorMessage}\n\nPlease try again with:\n• Better lighting\n• Clearer view of the object\n• Different angle`,
        [
          { text: 'OK', style: 'default' },
          { text: 'Try Again', onPress: () => {
            // Reset scanning state to allow retry
            setIsScanning(false);
          }}
        ]
      );
    }
  }

  function showResults(result: IdentificationResult) {
    const confidencePercent = Math.round(result.confidence * 100);

    let message = `${result.description || 'Object identified!'}\n\nConfidence: ${confidencePercent}%`;

    // Add category information
    if (result.category && result.category !== 'unknown') {
      message += `\nCategory: ${result.category.charAt(0).toUpperCase() + result.category.slice(1)}`;
    }

    // Add additional info if available
    if (result.additionalInfo) {
      if (result.additionalInfo.brand) {
        message += `\nBrand: ${result.additionalInfo.brand}`;
      }
      if (result.additionalInfo.type) {
        message += `\nType: ${result.additionalInfo.type}`;
      }
    }

    // Add purchase options info
    if (result.purchaseLinks && result.purchaseLinks.length > 0) {
      message += `\n\n🛒 ${result.purchaseLinks.length} shopping options available!`;
    }

    const buttons = [
      { text: 'OK', style: 'default' as const }
    ];

    // Add shopping button if purchase links are available
    if (result.purchaseLinks && result.purchaseLinks.length > 0) {
      buttons.push({
        text: 'Shop Now',
        onPress: () => {
          // Show shopping options
          showShoppingOptions(result);
        }
      });
    }

    // Add details button for more info
    buttons.push({
      text: 'View Details',
      onPress: () => {
        showDetailedResults(result);
      }
    });

    Alert.alert(
      result.name,
      message,
      buttons
    );
  }

  function showShoppingOptions(result: IdentificationResult) {
    if (!result.purchaseLinks || result.purchaseLinks.length === 0) return;

    const options = result.purchaseLinks.map(link => ({
      text: link.platform,
      onPress: () => {
        console.log(`Opening ${link.platform}: ${link.url}`);
        // TODO: Open URL in browser
      }
    }));

    options.push({ text: 'Cancel', style: 'cancel' as const });

    Alert.alert(
      'Shop for this item',
      'Choose where you\'d like to shop:',
      options
    );
  }

  function showDetailedResults(result: IdentificationResult) {
    let details = `Name: ${result.name}\n`;
    details += `Category: ${result.category}\n`;
    details += `Confidence: ${Math.round(result.confidence * 100)}%\n`;

    if (result.description) {
      details += `\nDescription:\n${result.description}\n`;
    }

    if (result.additionalInfo) {
      details += '\nAdditional Information:\n';
      Object.entries(result.additionalInfo).forEach(([key, value]) => {
        if (value && typeof value === 'string') {
          details += `• ${key.charAt(0).toUpperCase() + key.slice(1)}: ${value}\n`;
        }
      });
    }

    Alert.alert('Detailed Results', details, [{ text: 'OK' }]);
  }

  if (isWebMode) {
    // Web version - image upload only
    return (
      <SafeAreaView style={styles.container}>
        <ThemedView style={styles.webContainer}>
          <View style={styles.topBar}>
            <ThemedText style={styles.title}>What's That?</ThemedText>
          </View>

          <View style={styles.scanArea}>
            <View style={styles.webScanFrame}>
              <IconSymbol name="camera" size={80} color="#ccc" />
              <ThemedText style={styles.webScanText}>
                Camera not available on web
              </ThemedText>
              <ThemedText style={styles.webScanSubtext}>
                Upload an image to identify objects
              </ThemedText>
            </View>
          </View>

          <View style={styles.bottomBar}>
            <TouchableOpacity
              style={[styles.webUploadButton, isScanning && styles.captureButtonDisabled]}
              onPress={pickImage}
              disabled={isScanning}
            >
              {isScanning ? (
                <ActivityIndicator size="large" color="white" />
              ) : (
                <>
                  <IconSymbol name="photo" size={30} color="white" />
                  <Text style={styles.webUploadText}>Upload Image</Text>
                </>
              )}
            </TouchableOpacity>
          </View>
        </ThemedView>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <CameraView
        style={styles.camera}
        facing={facing}
        ref={cameraRef}
      />
      <View style={styles.overlay}>
        <View style={styles.topBar}>
          <ThemedText style={styles.title}>What's That?</ThemedText>
        </View>

        <View style={styles.scanArea}>
          <View style={styles.scanFrame} />
          <ThemedText style={styles.scanText}>
            Point camera at any object to identify it
          </ThemedText>
        </View>

        <View style={styles.bottomBar}>
          <TouchableOpacity style={styles.controlButton} onPress={pickImage}>
            <IconSymbol name="photo" size={30} color="white" />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.captureButton, isScanning && styles.captureButtonDisabled]}
            onPress={takePicture}
            disabled={isScanning}
          >
            {isScanning ? (
              <ActivityIndicator size="large" color="white" />
            ) : (
              <View style={styles.captureButtonInner} />
            )}
          </TouchableOpacity>

          <TouchableOpacity style={styles.controlButton} onPress={toggleCameraFacing}>
            <IconSymbol name="camera.rotate" size={30} color="white" />
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    gap: 20,
  },
  permissionText: {
    textAlign: 'center',
    fontSize: 16,
  },
  permissionButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  permissionButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  camera: {
    flex: 1,
  },
  overlay: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  topBar: {
    paddingTop: 60,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    textShadow: '-1px 1px 10px rgba(0, 0, 0, 0.75)',
  },
  scanArea: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  scanFrame: {
    width: width * 0.7,
    height: width * 0.7,
    borderWidth: 3,
    borderColor: 'white',
    borderRadius: 20,
    backgroundColor: 'transparent',
    marginBottom: 20,
  },
  scanText: {
    color: 'white',
    fontSize: 16,
    textAlign: 'center',
    textShadow: '-1px 1px 10px rgba(0, 0, 0, 0.75)',
  },
  bottomBar: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingBottom: 40,
    paddingHorizontal: 20,
  },
  controlButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  captureButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 4,
    borderColor: 'white',
  },
  captureButtonDisabled: {
    opacity: 0.5,
  },
  captureButtonInner: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'white',
  },
  // Web-specific styles
  webContainer: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  webScanFrame: {
    width: width * 0.7,
    height: width * 0.7,
    borderWidth: 3,
    borderColor: '#ddd',
    borderRadius: 20,
    backgroundColor: 'white',
    marginBottom: 20,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  webScanText: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    color: '#666',
  },
  webScanSubtext: {
    fontSize: 14,
    textAlign: 'center',
    color: '#999',
  },
  webUploadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    backgroundColor: '#007AFF',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderRadius: 12,
  },
  webUploadText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});
