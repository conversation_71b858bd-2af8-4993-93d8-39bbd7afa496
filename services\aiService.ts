
export interface IdentificationResult {
  name: string;
  category: string;
  confidence: number;
  description?: string;
  purchaseLinks?: PurchaseLink[];
  additionalInfo?: Record<string, any>;
}

export interface PurchaseLink {
  platform: string;
  url: string;
  price?: string;
  rating?: number;
}

class AIService {
  private readonly OPENAI_API_KEY = process.env.EXPO_PUBLIC_OPENAI_API_KEY;
  private readonly GOOGLE_VISION_API_KEY = process.env.EXPO_PUBLIC_GOOGLE_VISION_API_KEY;

  constructor() {
    this.validateConfiguration();
  }

  /**
   * Validate that required environment variables are configured
   */
  private validateConfiguration() {
    const warnings: string[] = [];

    if (!this.OPENAI_API_KEY) {
      warnings.push('OpenAI API key not configured');
    } else if (!this.OPENAI_API_KEY.startsWith('sk-')) {
      warnings.push('OpenAI API key appears to be invalid');
    }

    if (!this.GOOGLE_VISION_API_KEY) {
      warnings.push('Google Vision API key not configured');
    }

    if (warnings.length > 0) {
      console.warn('AI Service Configuration Issues:', warnings);
      if (warnings.length === 2) {
        console.warn('No AI services are properly configured. Object identification will not work.');
      }
    } else {
      console.log('AI Service configuration validated successfully');
    }
  }

  /**
   * Check if any AI service is available
   */
  public isConfigured(): boolean {
    return !!(this.OPENAI_API_KEY || this.GOOGLE_VISION_API_KEY);
  }

  /**
   * Identify an object from a base64 image using OpenAI Vision API
   */
  async identifyWithOpenAI(base64Image: string): Promise<IdentificationResult> {
    if (!this.OPENAI_API_KEY) {
      throw new Error('OpenAI API key not configured');
    }

    try {
      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.OPENAI_API_KEY}`,
        },
        body: JSON.stringify({
          model: 'gpt-4o-mini',
          messages: [
            {
              role: 'user',
              content: [
                {
                  type: 'text',
                  text: `Analyze this image and identify the main object. Be very specific about the product name, brand, and type.

                  Respond with ONLY a valid JSON object in this exact format (no markdown, no code blocks, no extra text):
                  {
                    "name": "specific product name with brand if visible",
                    "category": "product/plant/animal/landmark/food/etc",
                    "confidence": 0.95,
                    "description": "detailed description including brand, type, and key features",
                    "additionalInfo": {
                      "brand": "brand name if visible",
                      "type": "specific product type",
                      "uses": "primary uses or purpose",
                      "ingredients": "main ingredients if food product",
                      "size": "size or quantity if visible"
                    }
                  }

                  Be as specific as possible. For food products, identify the exact type (e.g., "Jif Creamy Peanut Butter" not "jarred goods"). Look for text, labels, and brand names in the image.`
                },
                {
                  type: 'image_url',
                  image_url: {
                    url: `data:image/jpeg;base64,${base64Image}`
                  }
                }
              ]
            }
          ],
          max_tokens: 800,
          temperature: 0.1
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`OpenAI API error: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      const content = data.choices[0]?.message?.content;

      if (!content) {
        throw new Error('No response from OpenAI');
      }

      // Clean the response - remove markdown code blocks if present
      let cleanContent = content.trim();
      if (cleanContent.startsWith('```json')) {
        cleanContent = cleanContent.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (cleanContent.startsWith('```')) {
        cleanContent = cleanContent.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }

      // Parse the JSON response
      let result;
      try {
        result = JSON.parse(cleanContent);
      } catch (parseError) {
        console.error('JSON parse error. Raw content:', content);
        console.error('Cleaned content:', cleanContent);
        throw new Error(`Failed to parse OpenAI response as JSON: ${parseError.message}`);
      }

      // Validate required fields
      if (!result.name || !result.category || typeof result.confidence !== 'number') {
        throw new Error('Invalid response format from OpenAI');
      }

      // Ensure confidence is between 0 and 1
      if (result.confidence > 1) {
        result.confidence = result.confidence / 100;
      }

      // Add purchase links if it's a product
      if (result.category === 'product' || result.category === 'food') {
        result.purchaseLinks = await this.generatePurchaseLinks(result.name);
      }

      return result;
    } catch (error) {
      console.error('OpenAI identification error:', error);
      throw new Error('Failed to identify object with OpenAI');
    }
  }

  /**
   * Identify an object using Google Vision API (fallback)
   */
  async identifyWithGoogleVision(base64Image: string): Promise<IdentificationResult> {
    if (!this.GOOGLE_VISION_API_KEY) {
      throw new Error('Google Vision API key not configured');
    }

    try {
      const response = await fetch(
        `https://vision.googleapis.com/v1/images:annotate?key=${this.GOOGLE_VISION_API_KEY}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            requests: [
              {
                image: {
                  content: base64Image,
                },
                features: [
                  { type: 'LABEL_DETECTION', maxResults: 15 },
                  { type: 'OBJECT_LOCALIZATION', maxResults: 10 },
                  { type: 'TEXT_DETECTION', maxResults: 10 },
                  { type: 'LOGO_DETECTION', maxResults: 5 },
                ],
              },
            ],
          }),
        }
      );

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Google Vision API error: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      const annotations = data.responses[0];

      if (annotations.error) {
        throw new Error(`Google Vision API error: ${annotations.error.message}`);
      }

      // Process the results
      const labels = annotations.labelAnnotations || [];
      const objects = annotations.localizedObjectAnnotations || [];
      const textAnnotations = annotations.textAnnotations || [];
      const logoAnnotations = annotations.logoAnnotations || [];

      if (labels.length === 0 && objects.length === 0) {
        throw new Error('No objects detected');
      }

      // Extract text for better identification
      const detectedText = textAnnotations.length > 0 ? textAnnotations[0].description : '';
      const brandNames = logoAnnotations.map(logo => logo.description).join(', ');

      // Combine information for better naming
      let name = 'Unknown Object';
      let confidence = 0.5;
      let category = 'unknown';

      // Prioritize specific objects over generic labels
      if (objects.length > 0) {
        const topObject = objects[0];
        name = topObject.name;
        confidence = topObject.score;
      } else if (labels.length > 0) {
        // Find the most specific label (usually food/product specific ones have higher scores)
        const specificLabels = labels.filter(label =>
          !['Product', 'Ingredient', 'Food', 'Tableware', 'Drinkware'].includes(label.description)
        );

        const topLabel = specificLabels.length > 0 ? specificLabels[0] : labels[0];
        name = topLabel.description;
        confidence = topLabel.score;
      }

      // Enhance name with detected text and brands
      if (brandNames) {
        name = `${brandNames} ${name}`;
      }

      // Use detected text to improve product identification
      if (detectedText && detectedText.length > 0) {
        const textWords = detectedText.split('\n').filter(word =>
          word.length > 2 && !word.match(/^\d+$/)
        );

        if (textWords.length > 0) {
          // Look for product names in the text
          const productKeywords = textWords.slice(0, 3).join(' ');
          if (productKeywords.length > name.length) {
            name = `${productKeywords} (${name})`;
          }
        }
      }

      category = this.categorizeObject(name, labels);

      const result: IdentificationResult = {
        name: name.trim(),
        category,
        confidence,
        description: `Detected with ${Math.round(confidence * 100)}% confidence using Google Vision`,
        additionalInfo: {
          detectedText: detectedText ? detectedText.substring(0, 100) : undefined,
          brands: brandNames || undefined,
          labels: labels.slice(0, 5).map(l => l.description).join(', ')
        }
      };

      // Add purchase links if it's a product
      if (result.category === 'product' || result.category === 'food') {
        result.purchaseLinks = await this.generatePurchaseLinks(result.name);
      }

      return result;
    } catch (error) {
      console.error('Google Vision identification error:', error);
      throw new Error('Failed to identify object with Google Vision');
    }
  }

  /**
   * Main identification method that tries multiple services
   */
  async identifyObject(base64Image: string): Promise<IdentificationResult> {
    // Validate input
    if (!base64Image || base64Image.length < 100) {
      throw new Error('Invalid image data provided');
    }

    let openAIError: Error | null = null;
    let googleVisionError: Error | null = null;

    try {
      // Try OpenAI first (more accurate for specific product identification)
      console.log('Attempting identification with OpenAI...');
      const result = await this.identifyWithOpenAI(base64Image);
      console.log('OpenAI identification successful:', result.name);
      return result;
    } catch (error) {
      openAIError = error as Error;
      console.warn('OpenAI failed, trying Google Vision:', error);

      try {
        // Fallback to Google Vision
        console.log('Attempting identification with Google Vision...');
        const result = await this.identifyWithGoogleVision(base64Image);
        console.log('Google Vision identification successful:', result.name);
        return result;
      } catch (fallbackError) {
        googleVisionError = fallbackError as Error;
        console.error('All AI services failed');
        console.error('OpenAI error:', openAIError?.message);
        console.error('Google Vision error:', googleVisionError?.message);

        // Return a helpful generic result
        return {
          name: 'Unknown Object',
          category: 'unknown',
          confidence: 0.1,
          description: 'Unable to identify this object. This could be due to:\n• Poor image quality or lighting\n• Object not clearly visible\n• Temporary AI service issues\n\nTry taking another photo with better lighting and a clear view of the object.',
          additionalInfo: {
            errors: {
              openai: openAIError?.message || 'Unknown error',
              googleVision: googleVisionError?.message || 'Unknown error'
            },
            suggestions: [
              'Ensure good lighting',
              'Get closer to the object',
              'Make sure the object fills most of the frame',
              'Try a different angle'
            ]
          }
        };
      }
    }
  }

  /**
   * Generate purchase links for products
   */
  private async generatePurchaseLinks(productName: string): Promise<PurchaseLink[]> {
    const links: PurchaseLink[] = [];
    
    // Amazon search link
    const amazonQuery = encodeURIComponent(productName);
    links.push({
      platform: 'Amazon',
      url: `https://www.amazon.com/s?k=${amazonQuery}&tag=whatsthat-20`, // Add your affiliate tag
    });

    // Google Shopping link
    const googleQuery = encodeURIComponent(productName);
    links.push({
      platform: 'Google Shopping',
      url: `https://www.google.com/search?tbm=shop&q=${googleQuery}`,
    });

    // eBay link
    const ebayQuery = encodeURIComponent(productName);
    links.push({
      platform: 'eBay',
      url: `https://www.ebay.com/sch/i.html?_nkw=${ebayQuery}`,
    });

    return links;
  }

  /**
   * Categorize an object based on its name and labels
   */
  private categorizeObject(name: string, labels?: any[]): string {
    const lowerName = name.toLowerCase();
    const labelText = labels ? labels.map(l => l.description.toLowerCase()).join(' ') : '';
    const combinedText = `${lowerName} ${labelText}`;

    // Food and beverages
    if (combinedText.match(/\b(food|snack|beverage|drink|meal|breakfast|lunch|dinner|dessert|candy|chocolate|cookie|cake|bread|milk|juice|soda|coffee|tea|peanut butter|jam|jelly|sauce|condiment|spice|cereal|pasta|rice|meat|cheese|yogurt|fruit|vegetable)\b/)) {
      return 'food';
    }

    // Plants and nature
    if (combinedText.match(/\b(plant|flower|tree|leaf|garden|botanical|succulent|herb|grass|bush|shrub|fern|moss|vine)\b/)) {
      return 'plant';
    }

    // Animals and pets
    if (combinedText.match(/\b(animal|dog|cat|bird|fish|pet|mammal|reptile|insect|wildlife|horse|cow|sheep|chicken|duck)\b/)) {
      return 'animal';
    }

    // Landmarks and architecture
    if (combinedText.match(/\b(building|monument|landmark|architecture|church|museum|bridge|tower|castle|statue|memorial)\b/)) {
      return 'landmark';
    }

    // Electronics and technology
    if (combinedText.match(/\b(phone|computer|laptop|tablet|camera|television|speaker|headphone|electronic|device|gadget|appliance)\b/)) {
      return 'electronics';
    }

    // Clothing and accessories
    if (combinedText.match(/\b(clothing|shirt|pants|dress|shoe|hat|bag|jewelry|watch|accessory|fashion|apparel)\b/)) {
      return 'fashion';
    }

    // Books and media
    if (combinedText.match(/\b(book|magazine|newspaper|media|publication|novel|textbook|manual|guide)\b/)) {
      return 'media';
    }

    // Tools and hardware
    if (combinedText.match(/\b(tool|hammer|screwdriver|wrench|hardware|equipment|instrument|machinery)\b/)) {
      return 'tools';
    }

    // Vehicles
    if (combinedText.match(/\b(car|truck|motorcycle|bicycle|vehicle|automobile|transport|boat|plane|train)\b/)) {
      return 'vehicle';
    }

    // Default to product for monetization
    return 'product';
  }
}

export const aiService = new AIService();
