import { Alert } from 'react-native';

export interface IdentificationResult {
  name: string;
  category: string;
  confidence: number;
  description?: string;
  purchaseLinks?: PurchaseLink[];
  additionalInfo?: Record<string, any>;
}

export interface PurchaseLink {
  platform: string;
  url: string;
  price?: string;
  rating?: number;
}

class AIService {
  private readonly OPENAI_API_KEY = process.env.EXPO_PUBLIC_OPENAI_API_KEY;
  private readonly GOOGLE_VISION_API_KEY = process.env.EXPO_PUBLIC_GOOGLE_VISION_API_KEY;

  /**
   * Identify an object from a base64 image using OpenAI Vision API
   */
  async identifyWithOpenAI(base64Image: string): Promise<IdentificationResult> {
    if (!this.OPENAI_API_KEY) {
      throw new Error('OpenAI API key not configured');
    }

    try {
      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.OPENAI_API_KEY}`,
        },
        body: JSON.stringify({
          model: 'gpt-4o-mini',
          messages: [
            {
              role: 'user',
              content: [
                {
                  type: 'text',
                  text: `Identify this object and provide the following information in JSON format:
                  {
                    "name": "specific name of the object",
                    "category": "category (product/plant/animal/landmark/food/etc)",
                    "confidence": 0.95,
                    "description": "brief description",
                    "additionalInfo": {
                      "brand": "if applicable",
                      "model": "if applicable",
                      "uses": "common uses or purpose",
                      "care": "care instructions if plant/pet",
                      "location": "if landmark"
                    }
                  }
                  
                  Be specific and accurate. If you're not sure, lower the confidence score.`
                },
                {
                  type: 'image_url',
                  image_url: {
                    url: `data:image/jpeg;base64,${base64Image}`
                  }
                }
              ]
            }
          ],
          max_tokens: 500
        })
      });

      if (!response.ok) {
        throw new Error(`OpenAI API error: ${response.status}`);
      }

      const data = await response.json();
      const content = data.choices[0]?.message?.content;
      
      if (!content) {
        throw new Error('No response from OpenAI');
      }

      // Parse the JSON response
      const result = JSON.parse(content);
      
      // Add purchase links if it's a product
      if (result.category === 'product') {
        result.purchaseLinks = await this.generatePurchaseLinks(result.name);
      }

      return result;
    } catch (error) {
      console.error('OpenAI identification error:', error);
      throw new Error('Failed to identify object with OpenAI');
    }
  }

  /**
   * Identify an object using Google Vision API (fallback)
   */
  async identifyWithGoogleVision(base64Image: string): Promise<IdentificationResult> {
    if (!this.GOOGLE_VISION_API_KEY) {
      throw new Error('Google Vision API key not configured');
    }

    try {
      const response = await fetch(
        `https://vision.googleapis.com/v1/images:annotate?key=${this.GOOGLE_VISION_API_KEY}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            requests: [
              {
                image: {
                  content: base64Image,
                },
                features: [
                  { type: 'LABEL_DETECTION', maxResults: 10 },
                  { type: 'OBJECT_LOCALIZATION', maxResults: 10 },
                  { type: 'TEXT_DETECTION', maxResults: 5 },
                ],
              },
            ],
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`Google Vision API error: ${response.status}`);
      }

      const data = await response.json();
      const annotations = data.responses[0];

      // Process the results
      const labels = annotations.labelAnnotations || [];
      const objects = annotations.localizedObjectAnnotations || [];
      
      if (labels.length === 0 && objects.length === 0) {
        throw new Error('No objects detected');
      }

      // Get the most confident result
      const topLabel = labels[0];
      const topObject = objects[0];
      
      const name = topObject?.name || topLabel?.description || 'Unknown Object';
      const confidence = topObject?.score || topLabel?.score || 0.5;
      
      const result: IdentificationResult = {
        name,
        category: this.categorizeObject(name),
        confidence,
        description: `Detected with ${Math.round(confidence * 100)}% confidence`,
      };

      // Add purchase links if it's a product
      if (result.category === 'product') {
        result.purchaseLinks = await this.generatePurchaseLinks(result.name);
      }

      return result;
    } catch (error) {
      console.error('Google Vision identification error:', error);
      throw new Error('Failed to identify object with Google Vision');
    }
  }

  /**
   * Main identification method that tries multiple services
   */
  async identifyObject(base64Image: string): Promise<IdentificationResult> {
    try {
      // Try OpenAI first (more accurate for general objects)
      return await this.identifyWithOpenAI(base64Image);
    } catch (error) {
      console.warn('OpenAI failed, trying Google Vision:', error);
      
      try {
        // Fallback to Google Vision
        return await this.identifyWithGoogleVision(base64Image);
      } catch (fallbackError) {
        console.error('All AI services failed:', fallbackError);
        
        // Return a generic result
        return {
          name: 'Unknown Object',
          category: 'unknown',
          confidence: 0.1,
          description: 'Unable to identify this object. Please try again with better lighting or a clearer image.',
        };
      }
    }
  }

  /**
   * Generate purchase links for products
   */
  private async generatePurchaseLinks(productName: string): Promise<PurchaseLink[]> {
    const links: PurchaseLink[] = [];
    
    // Amazon search link
    const amazonQuery = encodeURIComponent(productName);
    links.push({
      platform: 'Amazon',
      url: `https://www.amazon.com/s?k=${amazonQuery}&tag=whatsthat-20`, // Add your affiliate tag
    });

    // Google Shopping link
    const googleQuery = encodeURIComponent(productName);
    links.push({
      platform: 'Google Shopping',
      url: `https://www.google.com/search?tbm=shop&q=${googleQuery}`,
    });

    // eBay link
    const ebayQuery = encodeURIComponent(productName);
    links.push({
      platform: 'eBay',
      url: `https://www.ebay.com/sch/i.html?_nkw=${ebayQuery}`,
    });

    return links;
  }

  /**
   * Categorize an object based on its name
   */
  private categorizeObject(name: string): string {
    const lowerName = name.toLowerCase();
    
    if (lowerName.includes('plant') || lowerName.includes('flower') || lowerName.includes('tree')) {
      return 'plant';
    }
    if (lowerName.includes('animal') || lowerName.includes('dog') || lowerName.includes('cat') || lowerName.includes('bird')) {
      return 'animal';
    }
    if (lowerName.includes('building') || lowerName.includes('monument') || lowerName.includes('landmark')) {
      return 'landmark';
    }
    if (lowerName.includes('food') || lowerName.includes('drink') || lowerName.includes('meal')) {
      return 'food';
    }
    
    return 'product'; // Default to product for monetization
  }
}

export const aiService = new AIService();
